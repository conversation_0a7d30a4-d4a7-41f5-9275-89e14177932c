<?php
// Simple debug script to test redirect behavior
session_start();

$url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https://" : "http://") 
      . $_SERVER['HTTP_HOST'] 
      . '/coaching/center.php?slug=test&debug=1';

echo "<h1>Redirect Test</h1>";
echo "<p>Attempting to redirect to: $url</p>";

// Log to file
$log = "Attempting redirect to: $url at " . date('Y-m-d H:i:s') . "\n";
file_put_contents('redirect_debug.log', $log, FILE_APPEND);

// Perform redirect
header("Location: $url", true, 303);
exit;
?>

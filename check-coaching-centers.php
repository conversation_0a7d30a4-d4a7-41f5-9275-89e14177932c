<?php
/**
 * Check Coaching Centers in Database
 */
require_once 'includes/autoload.php';

echo "<h1>Coaching Centers in Database</h1>";

try {
    $db = Database::getInstance();
    
    // Get all coaching centers
    $coachingCenters = $db->fetchAll("SELECT coaching_id, coaching_name, slug, status FROM coaching_centers ORDER BY coaching_id LIMIT 10");
    
    if (empty($coachingCenters)) {
        echo "<p>No coaching centers found in the database.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Name</th><th>Slug</th><th>Status</th><th>Test Link</th></tr>";
        
        foreach ($coachingCenters as $center) {
            echo "<tr>";
            echo "<td>" . $center['coaching_id'] . "</td>";
            echo "<td>" . htmlspecialchars($center['coaching_name']) . "</td>";
            echo "<td>" . htmlspecialchars($center['slug']) . "</td>";
            echo "<td>" . htmlspecialchars($center['status']) . "</td>";
            echo "<td><a href='center.php?slug=" . urlencode($center['slug']) . "' target='_blank'>View Center</a></td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Check enquiries table structure
    echo "<h2>Enquiries Table Structure</h2>";
    $columns = $db->fetchAll("DESCRIBE enquiries");
    
    if (empty($columns)) {
        echo "<p>Enquiries table does not exist or has no columns.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>

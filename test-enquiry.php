<?php
/**
 * Test Enquiry Form Submission
 */
require_once 'includes/autoload.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Testing Enquiry Form Submission</h1>";

// Test 1: Check if sanitize function exists
echo "<h2>Test 1: Function Availability</h2>";
if (function_exists('sanitize')) {
    echo "✓ sanitize() function exists<br>";
} else {
    echo "✗ sanitize() function does not exist<br>";
}

// Test 2: Check database connection
echo "<h2>Test 2: Database Connection</h2>";
try {
    $db = Database::getInstance();
    echo "✓ Database connection successful<br>";
    
    // Test if enquiries table exists
    $result = $db->query("SHOW TABLES LIKE 'enquiries'");
    if ($result) {
        echo "✓ enquiries table exists<br>";
        $result->close();
    } else {
        echo "✗ enquiries table does not exist<br>";
    }
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "<br>";
}

// Test 3: Check CoachingCenter class
echo "<h2>Test 3: CoachingCenter Class</h2>";
try {
    $coachingObj = new CoachingCenter();
    echo "✓ CoachingCenter class instantiated<br>";
    
    // Try to get a coaching center by ID
    $coaching = $coachingObj->getById(1);
    if ($coaching) {
        echo "✓ Found coaching center with ID 1: " . $coaching['coaching_name'] . "<br>";
        echo "✓ Slug: " . $coaching['slug'] . "<br>";
    } else {
        echo "✗ No coaching center found with ID 1<br>";
    }
} catch (Exception $e) {
    echo "✗ CoachingCenter class error: " . $e->getMessage() . "<br>";
}

// Test 4: Simulate form submission
echo "<h2>Test 4: Simulate Form Submission</h2>";
$_POST = [
    'coaching_id' => '1',
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'phone' => '1234567890',
    'message' => 'Test enquiry message',
    'course' => '',
    'location_id' => ''
];

echo "Simulated POST data:<br>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

// Test the validation logic
$errors = [];
if (empty($_POST['name'])) $errors[] = "Name is required";
if (empty($_POST['email']) || !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Valid email is required";
}
if (empty($_POST['phone'])) $errors[] = "Phone is required";

if (empty($errors)) {
    echo "✓ Validation passed<br>";
} else {
    echo "✗ Validation failed: " . implode(", ", $errors) . "<br>";
}

// Test sanitize function
if (function_exists('sanitize')) {
    $sanitizedName = sanitize($_POST['name']);
    echo "✓ Sanitized name: " . $sanitizedName . "<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p><a href='center.php?slug=test'>Go back to center page</a></p>";
?>

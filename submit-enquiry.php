<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set absolute path for debug log
$log_dir = __DIR__ . '/logs';
$debug_log = $log_dir . '/submit_enquiry_debug.log';

// Verify log directory exists and is writable
if (!file_exists($log_dir)) {
    if (!mkdir($log_dir, 0755, true)) {
        die('Failed to create logs directory');
    }
}

file_put_contents($debug_log, "=== Starting Enquiry Processing ===\n", FILE_APPEND);
error_log("Log file location: " . $debug_log); // Output path to error_log for debugging

function logDebug($message) {
    global $debug_log;
    $message = date('[Y-m-d H:i:s] ') . $message . "\n";
    file_put_contents($debug_log, $message, FILE_APPEND);
    error_log(trim($message)); // Also log to standard error log
}

logDebug("Session started");
logDebug("Request Method: " . $_SERVER['REQUEST_METHOD']);

// Include required files
require_once 'includes/classes/Database.php';
require_once 'includes/classes/CoachingCenter.php';
require_once 'includes/functions/helpers.php';

try {
    $db = new Database();
    $coachingObj = new CoachingCenter();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        logDebug("POST Data: " . json_encode($_POST));

        // Get coaching ID and validate
        $coaching_id = (int)($_POST['coaching_id'] ?? 0);
        $coaching = $coachingObj->getById($coaching_id);
        
        if (!$coaching || empty($coaching['slug'])) {
            throw new Exception("Invalid coaching center ID: $coaching_id");
        }
        
        $slug = $coaching['slug'];
        $base_url = (isset($_SERVER['HTTPS']) ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'];
        $redirect_url = "$base_url/coaching/center.php?slug=$slug";
        logDebug("Redirect URL set to: $redirect_url");

        // Validate form data
        $errors = [];
        if (empty($_POST['name'])) $errors[] = "Name is required";
        if (empty($_POST['email']) || !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Valid email is required";
        }
        if (empty($_POST['phone'])) $errors[] = "Phone is required";

        if (!empty($errors)) {
            $_SESSION['enquiry_errors'] = $errors;
            logDebug("Validation failed: " . implode(", ", $errors));
            header("Location: $redirect_url&enquiry=error", true, 303);
            exit;
        }

        // Save to database
        $stmt = $db->prepare(
            "INSERT INTO enquiries 
            (coaching_id, course_id, location_id, name, email, phone, message, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())"
        );
        
        $result = $stmt->execute([
            $coaching_id,
            $_POST['course'] ?? null,
            $_POST['location_id'] ?? null,
            sanitize_input($_POST['name']),
            sanitize_input($_POST['email']),
            sanitize_input($_POST['phone']),
            sanitize_input($_POST['message'] ?? '')
        ]);

        if (!$result) {
            throw new Exception("Database insert failed");
        }

        // Success
        unset($_SESSION['enquiry_errors']);
        logDebug("Enquiry successfully submitted");
        header("Location: $redirect_url&enquiry=success", true, 303);
        exit;
    }

    // If not POST request, redirect to home
    header("Location: index.php", true, 303);
    exit;
    
} catch (Exception $e) {
    logDebug("ERROR: " . $e->getMessage());
    $_SESSION['enquiry_error'] = "An error occurred. Please try again.";
    
    $redirect_url = (isset($base_url) ? $base_url : 'http://' . $_SERVER['HTTP_HOST']) . '/coaching/index.php';
    header("Location: $redirect_url", true, 303);
    exit;
}

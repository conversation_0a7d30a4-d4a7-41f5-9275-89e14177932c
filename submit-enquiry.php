<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Simple debug function that outputs to both file and screen
function debugOutput($message) {
    $timestamp = date('[Y-m-d H:i:s] ');
    $fullMessage = $timestamp . $message . "\n";

    // Output to screen for immediate debugging
    echo "<pre>" . htmlspecialchars($fullMessage) . "</pre>";

    // Also log to file
    $log_file = __DIR__ . '/debug_enquiry.log';
    file_put_contents($log_file, $fullMessage, FILE_APPEND | LOCK_EX);

    // Force output
    if (ob_get_level()) {
        ob_flush();
    }
    flush();
}

debugOutput("=== ENQUIRY FORM DEBUG START ===");
debugOutput("Request Method: " . $_SERVER['REQUEST_METHOD']);
debugOutput("POST Data: " . print_r($_POST, true));
debugOutput("Session Data: " . print_r($_SESSION, true));

// Include required files
debugOutput("Including required files...");
try {
    require_once 'includes/classes/Database.php';
    debugOutput("Database class loaded");
    require_once 'includes/classes/CoachingCenter.php';
    debugOutput("CoachingCenter class loaded");
    require_once 'includes/functions/helpers.php';
    debugOutput("Helper functions loaded");
} catch (Exception $e) {
    debugOutput("ERROR loading files: " . $e->getMessage());
    die("Failed to load required files: " . $e->getMessage());
}

try {
    debugOutput("Creating database instance...");
    $db = Database::getInstance();
    debugOutput("Database instance created successfully");

    debugOutput("Creating CoachingCenter instance...");
    $coachingObj = new CoachingCenter();
    debugOutput("CoachingCenter instance created successfully");
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        debugOutput("Processing POST request");
        debugOutput("POST coaching_id: " . ($_POST['coaching_id'] ?? 'NOT SET'));

        // Get coaching ID and validate
        $coaching_id = (int)($_POST['coaching_id'] ?? 0);
        debugOutput("Parsed coaching_id: " . $coaching_id);

        if ($coaching_id <= 0) {
            debugOutput("ERROR: Invalid coaching_id: " . $coaching_id);
            throw new Exception("Invalid coaching center ID: $coaching_id");
        }

        debugOutput("Getting coaching center by ID: " . $coaching_id);
        $coaching = $coachingObj->getById($coaching_id);
        debugOutput("Coaching center result: " . print_r($coaching, true));

        if (!$coaching || empty($coaching['slug'])) {
            debugOutput("ERROR: Coaching center not found or missing slug");
            throw new Exception("Invalid coaching center ID: $coaching_id");
        }
        
        $slug = $coaching['slug'];
        debugOutput("Coaching slug: " . $slug);

        $base_url = (isset($_SERVER['HTTPS']) ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'];
        $script_dir = dirname($_SERVER['SCRIPT_NAME']);
        $redirect_url = "$base_url$script_dir/center.php?slug=$slug";
        debugOutput("Redirect URL constructed: " . $redirect_url);

        // Validate form data
        debugOutput("Starting form validation...");
        $errors = [];
        if (empty($_POST['name'])) $errors[] = "Name is required";
        if (empty($_POST['email']) || !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Valid email is required";
        }
        if (empty($_POST['phone'])) $errors[] = "Phone is required";

        if (!empty($errors)) {
            debugOutput("Validation failed: " . implode(", ", $errors));
            $_SESSION['enquiry_errors'] = $errors;
            $error_redirect = $redirect_url . "&enquiry=error";
            debugOutput("Redirecting to: " . $error_redirect);
            header("Location: $error_redirect", true, 303);
            exit;
        }

        debugOutput("Form validation passed");

        // Save to database
        debugOutput("Preparing database insert...");
        $sql = "INSERT INTO enquiries
                (coaching_id, course_id, location_id, name, email, phone, message, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";

        $params = [
            $coaching_id,
            $_POST['course'] ?? null,
            $_POST['location_id'] ?? null,
            sanitize($_POST['name']),
            sanitize($_POST['email']),
            sanitize($_POST['phone']),
            sanitize($_POST['message'] ?? '')
        ];

        debugOutput("SQL: " . $sql);
        debugOutput("Parameters: " . print_r($params, true));

        $stmt = $db->query($sql, $params);

        if (!$stmt) {
            debugOutput("Database insert failed");
            throw new Exception("Database insert failed: " . $db->getLastError());
        }

        debugOutput("Database insert successful");
        $stmt->close();

        // Success
        unset($_SESSION['enquiry_errors']);
        $success_redirect = $redirect_url . "&enquiry=success";
        debugOutput("Enquiry successfully submitted. Redirecting to: " . $success_redirect);
        header("Location: $success_redirect", true, 303);
        exit;
    } else {
        // If not POST request, redirect to home
        debugOutput("Not a POST request, redirecting to index.php");
        header("Location: index.php", true, 303);
        exit;
    }

} catch (Exception $e) {
    debugOutput("EXCEPTION CAUGHT: " . $e->getMessage());
    debugOutput("Stack trace: " . $e->getTraceAsString());
    $_SESSION['enquiry_error'] = "An error occurred. Please try again.";

    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    $error_redirect = (isset($base_url) ? $base_url : 'http://' . $_SERVER['HTTP_HOST']) . $script_dir . '/index.php';
    debugOutput("Exception redirect to: " . $error_redirect);
    header("Location: $error_redirect", true, 303);
    exit;
}

debugOutput("=== ENQUIRY FORM DEBUG END ===");
